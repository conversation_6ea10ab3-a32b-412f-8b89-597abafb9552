 The issue is confirmed by examining the low-level assembly code in finalizeUpgradeV2(): CSModule.sol:158-164

This assembly code zeros out the root slot of the _queueByPriority mapping, but this only makes the mapping entries unreachable - it doesn't actually delete the underlying QueueLib.Queue structures stored in those slots.

Impact on Queue Operations
The QueueLib.Queue operations become broken because peek() and dequeue() rely on accessing queue data through the mapping: QueueLib.sol:220-222

When the mapping root is zeroed, peek() returns a nil Batch because self.queue[self.head] now accesses an empty slot instead of the actual queue data.

Impact on Deposit Functionality
The obtainDepositData() function iterates through priority queues and calls queue.peek() to get batches: CSModule.sol:896-900

Since peek() always returns nil after the upgrade, no keys can be obtained from any queue. When loadedKeysCount != depositsCount, the function reverts with NotEnoughKeys: CSModule.sol:974-976

Impact on Depositable Validators Count
The _depositableValidatorsCount can never reach zero because the queue cleaning mechanisms that would normally decrement this counter cannot function. The cleanDepositQueue() function relies on queue operations that are now broken, and obtainDepositData() cannot consume keys to decrement the count.

This creates a permanent state where _depositableValidatorsCount > 0 but no actual keys can be obtained, causing all deposit attempts to fail network-wide.

Notes
The issue stems from the fundamental difference between zeroing a mapping's root slot (which only affects accessibility) versus properly cleaning up the data structures. The _legacyQueue field is also affected since it's a direct QueueLib.Queue instance that gets zeroed. The test file test/fork/vote-upgrade/V2Upgrade.sol shows upgrade testing but may not catch this specific queue orphaning issue if it doesn't test queue operations post-upgrade.

rm -f localhost.json && export RPC_URL="https://eth.llamarpc.com" && just test-poc

export RPC_URL="https://eth.llamarpc.com"
just test-poc

cd /home/<USER>/2025-07-lido-finance && export CHAIN=mainnet && export RPC_URL=https://eth.llamarpc.com && just test-poc